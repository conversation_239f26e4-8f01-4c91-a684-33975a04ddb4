{"theme": "De<PERSON>ult Light", "selectedAuthType": "oauth-personal", "mcpServers": {"memory": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "sequential-thinking": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "alwaysAllow": ["sequentialthinking"]}, "context7": {"type": "stdio", "command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"CONTEXT7_API_URL": "https://api.context7.com"}, "alwaysAllow": ["resolve-library-id", "get-library-docs"]}, "fs-mcp-server": {"type": "stdio", "command": "npx", "args": ["-y", "@bunas/fs-mcp@latest", "--API_KEY=asdqwezxc"], "alwaysAllow": ["read-file-21"]}, "browser-tools-mcp": {"type": "stdio", "command": "npx", "args": ["@agentdeskai/browser-tools-mcp@latest"]}, "edgeone-pages-mcp-server": {"type": "stdio", "command": "npx", "args": ["edgeone-pages-mcp"], "alwaysAllow": ["deploy_html", "deploy_folder_or_zip"]}, "playwright": {"type": "stdio", "command": "npx", "args": ["@playwright/mcp@latest"], "alwaysAllow": ["browser_close", "browser_resize", "browser_console_messages", "browser_handle_dialog", "browser_file_upload", "browser_install", "browser_press_key", "browser_navigate", "browser_pdf_save", "browser_network_requests", "browser_navigate_forward", "browser_navigate_back", "browser_take_screenshot", "browser_snapshot", "browser_click", "browser_drag", "browser_hover", "browser_type", "browser_select_option"]}, "@modelcontextprotocol/knowledge-graph-memory-server": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"]}, "filesystem": {"type": "stdio", "command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "d:\\jqfb2b\\jqfb2-b"], "alwaysAllow": ["list_allowed_directories", "get_file_info", "search_files", "move_file", "directory_tree", "list_directory", "list_directory_with_sizes", "create_directory", "edit_file", "write_file", "read_multiple_files", "read_file"], "timeout": 300}, "ssh-mpc-server": {"type": "stdio", "command": "npx", "args": ["-y", "@fangjunjie/ssh-mcp-server", "--host", "***************", "--port", "22", "--username", "root", "--password", "Ks81GHXkAd^U2x@_@"], "alwaysAllow": ["execute-command", "upload", "download"]}}}