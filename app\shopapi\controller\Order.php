<?php


namespace app\shopapi\controller;


use app\common\basics\ShopApi;
use app\common\server\JsonServer;
use app\shopapi\validate\VirtualDeliveryValidate;
use app\shopapi\logic\OrderLogic;
use app\shopapi\validate\OrderValidate;
use app\shop\logic\RefundAddressLogic;

/**
 * 商家移动端订单管理控制器
 * Class Order
 * @package app\shopapi\controller
 */
class Order extends ShopApi
{
    /**
     * @notes 订单列表
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 3:14 下午
     */
    public function lists()
    {
        $get = $this->request->get();
        $result = OrderLogic::lists($get, $this->page_no, $this->page_size, $this->shop_id);
        return JsonServer::success('获取成功', $result);
    }

    /**
     * @notes 订单详情
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/10 4:16 下午
     */
    public function detail()
    {
        $get = $this->request->get();
        $get['shop_id'] = $this->shop_id;
        // 修改验证方法调用方式
        // 原来的代码:
        // (new OrderValidate())->goCheck('detail', $get);
        
        // 修改为:
        (new OrderValidate())->scene('detail')->check($get);
        
        // 同样修改其他几处 goCheck 调用
        $result = OrderLogic::detail($get['id']);
        return JsonServer::success('获取成功', $result);
    }

    /**
     * @notes 取消订单
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/10 5:06 下午
     */
    public function cancel()
    {
        $post = $this->request->post();
        $post['shop_id'] = $this->shop_id;
        (new OrderValidate())->goCheck('cancel', $post);
        $result = OrderLogic::cancel($post['id'],$this->admin_id);
        if (true !== $result) {
            return JsonServer::error($result);
        }
        return JsonServer::success('操作成功');
    }

    /**
     * @notes 删除订单
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 5:37 下午
     */
    public function del()
    {
        $post = $this->request->post();
        $post['shop_id'] = $this->shop_id;
        (new OrderValidate())->goCheck('del', $post);
        OrderLogic::del($post['id'],$this->admin_id);
        return JsonServer::success('操作成功');
    }

    /**
     * @notes 修改地址
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/10 6:36 下午
     */
    public function editAddress()
    {
        $post = $this->request->post();
        $post['shop_id'] = $this->shop_id;
        (new OrderValidate())->goCheck('editAddress', $post);

        $result = OrderLogic::editAddress($post);
        if ($result === false) {
            return JsonServer::error(OrderLogic::getError() ?: '修改地址失败');
        }

        return JsonServer::success('操作成功');
    }

    /**
     * @notes 获取地址详情
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/13 11:41 上午
     */
    public function getAddress()
    {
        $get = $this->request->get();
        $get['shop_id'] = $this->shop_id;
        (new OrderValidate())->goCheck('getAddress', $get);
        $result = OrderLogic::getAddress($get['id']);
        return JsonServer::success('获取成功', $result);
    }

    /**
     * @notes 发货
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/11 10:27 上午
     */
    public function delivery()
    {
        $post = $this->request->post();
        $post['shop_id'] = $this->shop_id;
        (new OrderValidate())->goCheck('delivery', $post);
        $result = OrderLogic::delivery($post,$this->admin_id);
        if (true !== $result) {
            return JsonServer::error($result);
        }
        return JsonServer::success('操作成功');
    }

    /**
     * @notes 获取物流公司列表
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/13 11:46 上午
     */
    public function getExpress()
    {
        return JsonServer::success('获取成功', OrderLogic::getExpress());
    }

    /**
     * @notes 确认收货
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2021/11/11 10:56 上午
     */
    public function confirm()
    {
        $post = $this->request->post();
        $post['shop_id'] = $this->shop_id;
        (new OrderValidate())->goCheck('confirm', $post);
        OrderLogic::confirm($post['id'],$this->admin_id);
        return JsonServer::success('操作成功');
    }

    /**
     * @notes 查看物流
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/11 11:35 上午
     */
    public function logistics()
    {
        $get = $this->request->get();
        $get['shop_id'] = $this->shop_id;
        (new OrderValidate())->goCheck('logistics', $get);
        $result = OrderLogic::logistics($get['id']);
        return JsonServer::success('获取成功', $result);
    }


    /**
     * @notes 虚拟发货
     * @return \think\response\Json|void
     * <AUTHOR>
     * @date 2022/4/20 17:53
     */
    public function virtualDelivery()
    {
        $post = $this->request->post();
        (new VirtualDeliveryValidate())->goCheck();
        $result = OrderLogic::virtualDelivery($post, $this->admin_id);
        if (false == $result) {
            return JsonServer::error(OrderLogic::getError() ?: '发货失败');
        }
        return JsonServer::success('发货成功');
    }



    /**
     * @notes 确认付款
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/7/19 下午6:33
     */
    public function confirmPay()
    {
        $post = $this->request->post();
        if (!isset($post['order_id']) || empty($post['order_id'])) {
            return JsonServer::error('参数缺失');
        }
        $result = OrderLogic::confirmPay($post['order_id'], $this->admin_id);
        if(true !== $result) {
            return JsonServer::error($result);
        }
        return JsonServer::success('确认成功');
    }

    /**
 * @notes 退货地址列表
 * @return \think\response\Json
 */
public function refundAddressList()
{
    if (!$this->shop_id) {
        return JsonServer::error('未获取到商家信息');
    }
    
    $addresses = \app\shop\logic\RefundAddressLogic::lists($this->shop_id);
    return JsonServer::success('获取成功', $addresses);
}

/**
 * @notes 添加退货地址
 * @return \think\response\Json
 */
public function refundAddressAdd()
{
    $post = $this->request->post();
    $post['shop_id'] = $this->shop_id;
    
    // 验证参数
    (new \app\shopapi\validate\RefundAddressValidate())->goCheck('add', $post);
    
    $result = \app\shop\logic\RefundAddressLogic::add($post);
    if ($result === false) {
        return JsonServer::error(\app\shop\logic\RefundAddressLogic::getError() ?: '添加失败');
    }
    
    return JsonServer::success('添加成功');
}

/**
 * @notes 编辑退货地址
 * @return \think\response\Json
 */
public function refundAddressEdit()
{
    $post = $this->request->post();
    if (empty($post)) {
        // GET请求，获取详情
        $id = $this->request->get('id/d');
        if (empty($id)) {
            return JsonServer::error('缺少地址ID');
        }
        
        $detail = \app\shop\logic\RefundAddressLogic::detail($id, $this->shop_id);
        return JsonServer::success('', $detail);
    }
    
    // POST请求，更新数据
    $post['shop_id'] = $this->shop_id;
    
    // 验证参数
    (new \app\shopapi\validate\RefundAddressValidate())->goCheck('edit', $post);
    
    $result = \app\shop\logic\RefundAddressLogic::edit($post);
    if ($result === false) {
        return JsonServer::error(\app\shop\logic\RefundAddressLogic::getError() ?: '编辑失败');
    }
    
    return JsonServer::success('编辑成功');
}

/**
 * @notes 删除退货地址
 * @return \think\response\Json
 */
public function refundAddressDel()
{
    $id = $this->request->post('id/d');
    if (empty($id)) {
        return JsonServer::error('缺少地址ID');
    }
    
    $result = \app\shop\logic\RefundAddressLogic::del($id, $this->shop_id);
    if ($result === false) {
        return JsonServer::error(\app\shop\logic\RefundAddressLogic::getError() ?: '删除失败');
    }
    
    return JsonServer::success('删除成功');
}

/**
 * @notes 设置默认退货地址
 * @return \think\response\Json
 */
public function refundAddressSetDefault()
{
    $id = $this->request->post('id/d');
    if (empty($id)) {
        return JsonServer::error('缺少地址ID');
    }
    
    $result = \app\shop\logic\RefundAddressLogic::setDefault($id, $this->shop_id);
    if ($result === false) {
        return JsonServer::error(\app\shop\logic\RefundAddressLogic::getError() ?: '设置失败');
    }
    
    return JsonServer::success('设置成功');
}
}