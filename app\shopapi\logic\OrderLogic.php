<?php


namespace app\shopapi\logic;


use app\common\basics\Logic;
use app\common\enum\GoodsEnum;
use app\common\enum\NoticeEnum;
use app\common\enum\OrderEnum;
use app\common\enum\OrderLogEnum;
use app\common\enum\PayEnum;
use app\common\enum\TeamEnum;
use app\common\logic\GoodsVirtualLogic;
use app\common\logic\OrderLogLogic;
use app\common\logic\OrderRefundLogic;
use app\common\logic\PayNotifyLogic;
use app\common\model\Delivery;
use app\common\model\DevRegion;
use app\common\model\distribution\DistributionOrderGoods;
use app\common\model\Express;
use app\common\model\order\Order;
use app\common\model\team\TeamFound;
use app\common\model\team\TeamJoin;
use app\common\server\WechatMiniExpressSendSyncServer;
use think\facade\Db;

/**
 * 商家移动端订单管理逻辑层
 * Class OrderLogic
 * @package app\shopapi\logic
 */
class OrderLogic extends Logic
{
    /**
     * @notes 订单列表
     * @param $get
     * @param $page_no
     * @param $page_size
     * @param $shop_id
     * @return array
     * <AUTHOR>
     * @date 2021/11/10 3:13 下午
     */
    public static function lists($get, $page_no, $page_size, $shop_id)
    {
        $get['type'] = $get['type'] ?? 'all';
        $where[] = ['o.shop_id', '=', $shop_id];
        $where[] = ['o.del', '=', 0];
        $where[] = ['o.delete', '=', 0];
        //订单状态
        if (isset($get['type']) && !empty($get['type'])) {
            switch ($get['type']) {
                case 'pay':
                    $where[] = ['o.order_status', '=', 0];
                    break;
                case 'delivery':
                    $where[] = ['o.order_status', '=', 1];
                    break;
                case 'receiving':
                    $where[] = ['o.order_status', '=', 2];
                    break;
                case 'finish':
                    $where[] = ['o.order_status', '=', 3];
                    break;
                case 'close':
                    $where[] = ['o.order_status', '=', 4];
                    break;
            }
        }
        //订单商品名称
        if (isset($get['goods_name']) && !empty($get['goods_name'])) {
            $where[] = ['og.goods_name', 'like', '%'.$get['goods_name'].'%'];
        }

        $count = Order::alias('o')
            ->join('order_goods og', 'og.order_id = o.id')
            ->where($where)
            ->group('o.id')
            ->count();

        $lists = Order::alias('o')
            ->join('order_goods og', 'og.order_id = o.id')
            ->where($where)
            ->with(['order_goods'])
            ->field('o.id,o.order_type,o.order_sn,o.order_status,o.pay_status,o.shipping_status,o.order_amount,o.create_time,o.delivery_type, o.verification_status,o.pay_way')
            ->append(['is_team_success','shop_cancel_btn','edit_address_btn','to_ship_btn','take_btn','delivery_btn','del_btn','content_btn', 'to_verification_btn'])
            ->page($page_no, $page_size)
            ->order('o.id desc')
            ->group('o.id')
            ->select()
            ->toArray();

        foreach ($lists as &$item) {
            // 订单状态描述
            if ($item['order_status'] == OrderEnum::ORDER_STATUS_DELIVERY
                && $item['delivery_type'] == OrderEnum::DELIVERY_TYPE_SELF
                && $item['pay_status'] == PayEnum::ISPAID) {
                $item['order_status_text'] = '待取货';
            } else {
                $item['order_status_text'] = OrderEnum::getOrderStatus($item['order_status']);
            }
        }

        return [
            'list' => $lists,
            'page' => $page_no,
            'size' => $page_size,
            'count' => $count,
            'more' => is_more($count, $page_no, $page_size)
        ];
    }

    /**
     * @notes 订单详情
     * @param $id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/10 4:15 下午
     */
    public static function detail($id)
    {
        $result = Order::where('id',$id)
            ->field('id,user_id,order_sn,order_type,order_source,order_status,pay_status,pay_time,shipping_status,pay_way,order_amount,goods_price,shipping_price,discount_amount,member_amount,create_time,consignee,mobile,province,city,district,address,user_remark,delivery_type,delivery_content,pay_way')
            ->with(['order_goods','user', 'invoice'])
            ->append(['is_team_success','shop_cancel_btn','edit_address_btn','to_ship_btn','take_btn','delivery_btn','del_btn','order_status_text','pay_way_text','delivery_address','order_type_text','order_source_text','pay_status_text'])
            ->find()
            ->toArray();

        $result['pay_time'] = $result['pay_time'] ? date('Y-m-d H:i:s', $result['pay_time']) : '-';

        // 虚拟商品 发货内容
        if ($result['delivery_type'] != OrderEnum::DELIVERY_TYPE_VIRTUAL || $result['shipping_status'] != OrderEnum::SHIPPING_FINISH) {
            $result['delivery_content'] = '';
        }

        return $result;
    }

    /**
     * @notes 取消订单
     * @param $id
     * @param $admin_id
     * @return bool|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/10 5:06 下午
     */
    public static function cancel($id,$admin_id)
    {
        $order = Order::where(['id' => $id], ['orderGoods'])->find()->toArray();
        Db::startTrans();
        try {
            // 如果是拼团订单
            if ($order['order_type'] == OrderEnum::TEAM_ORDER) {
                $time = time();
                $team_id = (new TeamJoin())->where(['order_id' => $order['id']])->value('team_id');
                $teamJoin = (new TeamJoin())->alias('TJ')
                    ->field(['TJ.*,O.order_sn,O.order_status,O.pay_status,O.refund_status,O.order_amount'])
                    ->where(['team_id' => $team_id])
                    ->join('order O', 'O.id=TJ.order_id')
                    ->select()->toArray();

                TeamFound::update(['status' => TeamEnum::TEAM_STATUS_FAIL, 'team_end_time' => $time], ['id' => $team_id]);
                foreach ($teamJoin as $item) {
                    TeamJoin::update(['status' => TeamEnum::TEAM_STATUS_FAIL, 'update_time' => $time], ['id' => $item['id']]);
                    OrderRefundLogic::cancelOrder($item['order_id'], OrderLogEnum::TYPE_USER);  //取消订单

                    if ($item['pay_status'] == PayEnum::ISPAID) {
                        $order = (new Order())->findOrEmpty($item['order_id'])->toArray();
                        OrderRefundLogic::cancelOrderRefundUpdate($order); //更新订单状态
                        OrderRefundLogic::refund($order, $order['order_amount'], $order['order_amount']); //订单退款
                    }
                }


            } else {

                //取消订单
                OrderRefundLogic::cancelOrder($id, OrderLogEnum::TYPE_SHOP, $admin_id);
                //已支付的订单,取消,退款
                if ($order['pay_status'] == PayEnum::ISPAID) {
                    //更新订单状态
                    OrderRefundLogic::cancelOrderRefundUpdate($order);
                    //订单退款
                    OrderRefundLogic::refund($order, $order['order_amount'], $order['order_amount']);
                }
            }

            // 查找对应的分销订单置为失效状态
            if ($order['pay_status'] == PayEnum::ISPAID) {
                DistributionOrderGoods::where('order_id', $id)->update(['status' => 3]);
            }

            Db::commit();
            return true;

        } catch (\Exception $e) {
            Db::rollback();
            //增加退款失败记录
            if ($order['pay_status'] == PayEnum::ISPAID) {
                OrderRefundLogic::addErrorRefund($order, $e->getMessage());
            }
            return $e->getMessage();
        }
    }

    /**
     * @notes 删除订单
     * @param $id
     * @param $admin_id
     * <AUTHOR>
     * @date 2021/11/10 5:37 下午
     */
    public static function del($id,$admin_id)
    {
        Order::update(['delete'=>1,'update_time'=>time()], ['id'=>$id]);

        //订单日志
        OrderLogLogic::record(
            OrderLogEnum::TYPE_SHOP,
            OrderLogEnum::SHOP_DEL_ORDER,
            $id,
            $admin_id,
            OrderLogEnum::SHOP_DEL_ORDER
        );
    }

    /**
     * @notes 修改地址
     * @param $post
     * @return bool
     * <AUTHOR>
     * @date 2021/11/10 6:35 下午
     */
    public static function editAddress($post)
    {
        // 获取订单信息
        $order = Order::where('id', $post['id'])->find();
        if (!$order) {
            self::$error = '订单不存在';
            return false;
        }

        // 获取订单商品信息
        $orderGoods = Db::name('order_goods')->where('order_id', $post['id'])->select()->toArray();
        if (empty($orderGoods)) {
            self::$error = '订单商品信息不存在';
            return false;
        }

        // 构建原地址信息
        $originalAddress = [
            'province_id' => $order['province'],
            'city_id' => $order['city'],
            'district_id' => $order['district']
        ];

        // 构建新地址信息
        $newAddress = [
            'province_id' => $post['province'],
            'city_id' => $post['city'],
            'district_id' => $post['district']
        ];

        // 检查运费变化限制
        $checkResult = self::checkFreightChangeRestriction($orderGoods, $originalAddress, $newAddress, $order['shop_id'], $order['goods_price'] - $order['discount_amount']);
        if (!$checkResult['allow']) {
            self::$error = $checkResult['message'];
            return false;
        }

        Order::update(
            [
                'consignee'=>$post['consignee'],
                'province'=>$post['province'],
                'city'=>$post['city'],
                'district'=>$post['district'],
                'address'=>$post['address'],
                'mobile'=>$post['mobile'],
                'update_time' => time(),
            ], ['id'=>$post['id']]);

        return true;
    }

    /**
     * @notes 检查运费变化限制
     * @param array $orderGoods 订单商品信息
     * @param array $originalAddress 原地址信息
     * @param array $newAddress 新地址信息
     * @param int $shopId 店铺ID
     * @param float $orderAmount 订单金额（不含运费）
     * @return array
     * <AUTHOR> Assistant
     * @date 2025/01/26
     */
    private static function checkFreightChangeRestriction($orderGoods, $originalAddress, $newAddress, $shopId, $orderAmount)
    {
        try {
            // 计算原地址运费
            $originalFreight = self::calculateOrderFreight($orderGoods, $originalAddress, $shopId, $orderAmount);

            // 计算新地址运费
            $newFreight = self::calculateOrderFreight($orderGoods, $newAddress, $shopId, $orderAmount);

            // 只允许运费不发生变化的情况
            if ($originalFreight != $newFreight) {
                $originalFreightText = $originalFreight == 0 ? '包邮' : $originalFreight . '元';
                $newFreightText = $newFreight == 0 ? '包邮' : $newFreight . '元';

                return [
                    'allow' => false,
                    'message' => "修改地址会导致运费发生变化（原运费：{$originalFreightText}，新运费：{$newFreightText}），不允许修改地址"
                ];
            }

            return [
                'allow' => true,
                'message' => '允许修改'
            ];

        } catch (\Exception $e) {
            return [
                'allow' => false,
                'message' => '运费计算失败：' . $e->getMessage()
            ];
        }
    }

    /**
     * @notes 计算订单运费
     * @param array $orderGoods 订单商品信息
     * @param array $address 地址信息
     * @param int $shopId 店铺ID
     * @param float $orderAmount 订单金额（不含运费）
     * @return float
     * <AUTHOR> Assistant
     * @date 2025/01/26
     */
    private static function calculateOrderFreight($orderGoods, $address, $shopId, $orderAmount)
    {
        // 检查是否符合包邮活动条件
        $isFreeShipping = self::isFreeShipping($shopId, $orderAmount, $address);
        if ($isFreeShipping) {
            return 0;
        }

        $totalFreight = 0;
        $templateList = [];

        foreach ($orderGoods as $goods) {
            // 获取商品运费配置
            $goodsInfo = Db::name('goods')->where('id', $goods['goods_id'])
                ->field('express_type,express_money,express_template_id,type')
                ->find();

            if (!$goodsInfo) {
                continue;
            }

            // 虚拟商品不计算运费
            if ($goodsInfo['type'] == GoodsEnum::TYPE_VIRTUAL) {
                continue;
            }

            switch ($goodsInfo['express_type']) {
                case 1: // 包邮
                    break;
                case 2: // 统一运费
                    $totalFreight += $goodsInfo['express_money'] * $goods['goods_num'];
                    break;
                case 3: // 运费模板
                    if ($goodsInfo['express_template_id']) {
                        $templateList[$goodsInfo['express_template_id']][] = [
                            'goods_id' => $goods['goods_id'],
                            'item_id' => $goods['item_id'],
                            'num' => $goods['goods_num']
                        ];
                    }
                    break;
            }
        }

        // 计算运费模板的运费
        foreach ($templateList as $templateId => $templateGoods) {
            $freightInfo = Db::name('freight')->where('id', $templateId)->find();
            if (!$freightInfo) {
                continue;
            }

            $totalWeight = 0;
            $totalVolume = 0;
            $totalNum = 0;

            foreach ($templateGoods as $goodsInfo) {
                $goodsItem = Db::name('goods_item')->where('id', $goodsInfo['item_id'])
                    ->field('weight,volume')
                    ->find();

                if ($goodsItem) {
                    $totalWeight += ($goodsItem['weight'] ?: 0) * $goodsInfo['num'];
                    $totalVolume += ($goodsItem['volume'] ?: 0) * $goodsInfo['num'];
                }
                $totalNum += $goodsInfo['num'];
            }

            // 根据计费方式确定计费数量
            $nums = 0;
            switch ($freightInfo['charge_way']) {
                case 1: // 按重量计费
                    $nums = $totalWeight;
                    break;
                case 2: // 按体积计费
                    $nums = $totalVolume;
                    break;
                case 3: // 按件数计费
                    $nums = $totalNum;
                    break;
            }

            // 计算运费
            $freight = new \app\common\model\Freight();
            $templateFreight = $freight->sumFreight($address, $templateId, $nums);
            $totalFreight += $templateFreight;
        }

        return round($totalFreight, 2);
    }

    /**
     * @notes 是否满足包邮条件
     * @param $shopId
     * @param $orderAmount
     * @param $address
     * @return bool
     * <AUTHOR> Assistant
     * @date 2025/01/26
     */
    private static function isFreeShipping($shopId, $orderAmount, $address)
    {
        $config = Db::name('free_shipping_config')->where([
            'shop_id' => $shopId,
            'del' => 0
        ])->findOrEmpty();
        if (empty($config) || $config['status'] == 0) {
            // 未设置 或 未开启包邮活动
            return false;
        }
        // 校验区级设置
        $district = Db::name('free_shipping_region')->where([
            'shop_id' => $shopId,
            'del' => 0
        ])->whereFindInSet('region', $address['district_id'])->findOrEmpty();
        if (!empty($district) && $orderAmount >= $district['order_amount']) {
            // 符合包邮条件
            return true;
        }
        if (!empty($district) && $orderAmount < $district['order_amount']) {
            // 不符合条件,不再校验市级
            return false;
        }
        // 校验市级设置
        $city = Db::name('free_shipping_region')->where([
            'shop_id' => $shopId,
            'del' => 0
        ])->whereFindInSet('region', $address['city_id'])->findOrEmpty();
        if (!empty($city) && $orderAmount >= $city['order_amount']) {
            // 符合包邮条件
            return true;
        }
        if (!empty($city) && $orderAmount < $city['order_amount']) {
            // 不符合条件,不再校验省级
            return false;
        }

        // 校验省级设置
        $province = Db::name('free_shipping_region')->where([
            'shop_id' => $shopId,
            'del' => 0
        ])->whereFindInSet('region', $address['province_id'])->findOrEmpty();
        if (!empty($province) && $orderAmount >= $province['order_amount']) {
            // 符合包邮条件
            return true;
        }
        if (!empty($province) && $orderAmount < $province['order_amount']) {
            // 不符合条件,不再校验全国设置
            return false;
        }

        // 校验全国设置
        $all = Db::name('free_shipping_region')->where([
            'shop_id' => $shopId,
            'del' => 0,
            'region' => 'all'
        ])->findOrEmpty();
        if (!empty($all) && $orderAmount >= $all['order_amount']) {
            // 符合包邮条件
            return true;
        }
        return false;
    }

    /**
     * @notes 获取地址详情
     * @param $id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/13 11:41 上午
     */
    public static function getAddress($id)
    {
        $result = Order::where('id',$id)
            ->field('consignee,province,city,district,address,mobile')
            ->find()
            ->toArray();
        $result['region'] = DevRegion::where('id', 'in', $result['province'].','.$result['city'].','.$result['district'])->order('id','asc')->column('name');

        return $result;
    }

    /**
     * @notes 发货
     * @param $post
     * @param $admin_id
     * @return bool|string
     * <AUTHOR>
     * @date 2021/11/11 10:27 上午
     */
    public static function delivery($post, $admin_id)
    {
        Db::startTrans();
        try {
            $order = Order::where(['id' => $post['id']], ['order_goods'])->find();

            if ($order['shipping_status'] == 1) {
                return true;
            }


            //添加发货单
            $delivery_data = [
                'order_id' => $post['id'],
                'order_sn' => $order['order_sn'],
                'user_id' => $order['user_id'],
                'admin_id' => $admin_id,
                'consignee' => $order['consignee'],
                'mobile' => $order['mobile'],
                'province' => $order['province'],
                'city' => $order['city'],
                'district' => $order['district'],
                'address' => $order['address'],
                'invoice_no' => $post['invoice_no'] ?? '',
                'send_type' => $post['send_type'],
                'create_time' => time(),
            ];
            //配送方式->快递配送
            if ($post['send_type'] == 1) {
                $shipping = Express::where('id', $post['shipping_id'])->find();
                $delivery_data['shipping_id'] = $post['shipping_id'];
                $delivery_data['shipping_name'] = $shipping['name'];
                $delivery_data['shipping_status'] = 1;
            }
            $delivery = Delivery::create($delivery_data);


            //更新订单下商品的发货状态
            $order->update_time = time();
            $order->shipping_time = time();
            $order->shipping_status = 1;
            $order->order_status = Order::STATUS_WAIT_RECEIVE;
            $order->delivery_id = $delivery->id;
            $order->save();

            //订单日志
            OrderLogLogic::record(
                OrderLogEnum::TYPE_SHOP,
                OrderLogEnum::SHOP_DELIVERY_ORDER,
                $post['id'],
                $admin_id,
                OrderLogEnum::SHOP_DELIVERY_ORDER
            );
            $goods_id=Db::name('order_goods')->where('order_id', $post['id'])->value('goods_id');
            $goods_name=Db::name('goods')->where('id', $goods_id)->value('name');
            //通知用户发货
            if (!empty($order['mobile'])) {
                event('Notice', [
                    'scene' => NoticeEnum::ORDER_DELIVERY_NOTICE,
                    'mobile' => $order['mobile'],
                    'params' => [
                        'order_id' => $order['id'],
                        'user_id' => $order['user_id'],
                        'shipping_name' => $delivery_data['shipping_name'] ?? '无需快递',
                        'invoice_no' => $post['invoice_no'] ?? '',
                        'goods_name'=>$goods_name
                    ]
                ]);
            }

            // 同步微信小程序发货信息
            if ($order['pay_way'] == PayEnum::WECHAT_PAY) {
                try {
                    $orderData = Order::where('id', $post['id'])->find()->toArray();
                    WechatMiniExpressSendSyncServer::_sync_order($orderData);
                } catch (\Exception $e) {
                    // 记录日志但不影响发货流程
                    \think\facade\Log::write('微信发货信息同步失败: ' . $e->getMessage(), 'wechat_sync_error');
                }
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
    }

    /**
     * @notes 获取物流公司列表
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/13 11:46 上午
     */
    public static function getExpress()
    {
        return Express::where('del',0)->field('id,name')->select()->toArray();
    }

    /**
     * @notes 确认收货
     * @param $id
     * @param $admin_id
     * <AUTHOR>
     * @date 2021/11/11 10:55 上午
     */
    public static function confirm($id, $admin_id)
    {
        Order::update(
            [
                'order_status' => Order::STATUS_FINISH,
                'confirm_take_time' => time(),
                'update_time' => time(),
            ], ['id'=>$id]);

        //订单日志
        OrderLogLogic::record(
            OrderLogEnum::TYPE_SHOP,
            OrderLogEnum::SHOP_CONFIRM_ORDER,
            $id,
            $admin_id,
            OrderLogEnum::SHOP_CONFIRM_ORDER
        );

        // 同步微信小程序确认收货信息
        try {
            $orderData = Order::where('id', $id)->find()->toArray();
            if ($orderData['pay_way'] == PayEnum::WECHAT_PAY) {
                WechatMiniExpressSendSyncServer::_sync_order_confirm($orderData);
            }
        } catch (\Exception $e) {
            // 记录日志但不影响确认收货流程
            \think\facade\Log::write('微信确认收货信息同步失败: ' . $e->getMessage(), 'wechat_sync_error');
        }
    }

    /**
     * @notes 查看物流
     * @param $id
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2021/11/11 11:34 上午
     */
    public static function logistics($id)
    {
        return Delivery::where('order_id',$id)
            ->field('shipping_name,invoice_no')
            ->find()
            ->toArray();
    }


    /**
     * @notes 虚拟发货
     * @param $post
     * @param $admin_id
     * @return bool
     * <AUTHOR>
     * @date 2022/4/20 17:56
     */
    public static function virtualDelivery($post, $admin_id)
    {
        try {
            $order_id = $post['order_id'];
            $order = Order::with(['order_goods'])->where(['del' => 0, 'id' => $order_id])->find();

            // 更新发货订单信息
            $result = GoodsVirtualLogic::shopSelfDelivery($order_id,  $post['delivery_content']);
            if (true !== $result) {
                throw new \Exception($result);
            }

            //订单日志
            OrderLogLogic::record(
                OrderLogEnum::TYPE_SHOP,
                OrderLogEnum::SHOP_DELIVERY_ORDER,
                $order_id,
                $admin_id,
                OrderLogEnum::SHOP_DELIVERY_ORDER
            );
            $goods_id=Db::name('order_goods')->where('order_id', $order_id)->value('goods_id');
            $goods_name=Db::name('goods')->where('id', $goods_id)->value('name');
            //通知用户发货
            if (!empty($order['mobile'])) {
                event('Notice', [
                    'scene' => NoticeEnum::ORDER_DELIVERY_NOTICE,
                    'mobile' => $order['mobile'],
                    'params' => [
                        'order_id' => $order['id'],
                        'user_id' => $order['user_id'],
                        'shipping_name' => '无需快递',
                        'invoice_no' => '',
                        'goods_name'=>$goods_name
                    ]
                ]);
            }

            // 同步微信小程序发货信息
            if ($order['pay_way'] == PayEnum::WECHAT_PAY) {
                try {
                    $orderData = Order::where('id', $order_id)->find()->toArray();
                    WechatMiniExpressSendSyncServer::_sync_order($orderData);
                } catch (\Exception $e) {
                    // 记录日志但不影响发货流程
                    \think\facade\Log::write('微信虚拟发货信息同步失败: ' . $e->getMessage(), 'wechat_sync_error');
                }
            }

            return true;
        } catch (\Exception $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 确认付款
     * @param $order_id
     * @param $adminInfo
     * @return string|true
     * <AUTHOR>
     * @date 2024/7/19 下午6:32
     */
    public static function confirmPay($order_id, $adminInfo)
    {
        Db::startTrans();
        try {
            $order = Order::where(['id' => $order_id])->findOrEmpty()->toArray();
            $result = PayNotifyLogic::handle('order', $order['order_sn']);
            if (true !== $result) {
                throw new \Exception($result);
            }

            Db::commit();
            return true;
        } catch (\Exception $e) {
            Db::rollback();
            return $e->getMessage();
        }
    }
}