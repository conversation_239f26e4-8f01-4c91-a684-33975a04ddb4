#!/bin/bash

# 日志权限问题诊断和修复脚本
# 一键解决root用户创建日志文件导致的权限问题

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
}

# 获取项目根目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "========================================"
echo "    日志权限问题诊断和修复工具"
echo "========================================"
echo ""

log_info "项目目录: $PROJECT_DIR"

# 检查是否有sudo权限
if [ "$EUID" -ne 0 ]; then
    log_error "此脚本需要sudo权限运行"
    echo "请使用: sudo $0"
    exit 1
fi

# 检测Web用户
if id "www-data" &>/dev/null; then
    WEB_USER="www-data"
elif id "nginx" &>/dev/null; then
    WEB_USER="nginx"
elif id "apache" &>/dev/null; then
    WEB_USER="apache"
else
    WEB_USER="www"
fi

log_info "检测到的Web用户: $WEB_USER"

# 步骤1: 诊断当前问题
log_step "1. 诊断当前权限问题"
echo "----------------------------------------"

ISSUES_FOUND=0

# 检查runtime目录
if [ -d "$PROJECT_DIR/runtime" ]; then
    log_info "检查runtime目录权限..."
    
    # 查找不属于web用户的文件
    WRONG_OWNER_FILES=$(find "$PROJECT_DIR/runtime" ! -user $WEB_USER -type f 2>/dev/null | head -10)
    
    if [ -n "$WRONG_OWNER_FILES" ]; then
        log_warning "发现权限问题的文件:"
        echo "$WRONG_OWNER_FILES"
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    else
        log_success "runtime目录权限正常"
    fi
    
    # 检查目录权限
    RUNTIME_PERMS=$(stat -c "%a" "$PROJECT_DIR/runtime" 2>/dev/null)
    if [ "$RUNTIME_PERMS" != "755" ] && [ "$RUNTIME_PERMS" != "777" ]; then
        log_warning "runtime目录权限不正确: $RUNTIME_PERMS"
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    fi
else
    log_warning "runtime目录不存在"
fi

# 检查uploads目录
if [ -d "$PROJECT_DIR/public/uploads" ]; then
    log_info "检查uploads目录权限..."
    
    WRONG_UPLOAD_FILES=$(find "$PROJECT_DIR/public/uploads" ! -user $WEB_USER -type f 2>/dev/null | head -5)
    
    if [ -n "$WRONG_UPLOAD_FILES" ]; then
        log_warning "发现uploads权限问题的文件:"
        echo "$WRONG_UPLOAD_FILES"
        ISSUES_FOUND=$((ISSUES_FOUND + 1))
    else
        log_success "uploads目录权限正常"
    fi
fi

# 检查crontab配置
log_info "检查crontab配置..."
ROOT_CRON=$(crontab -l 2>/dev/null | grep -E "(php|think)" | wc -l)
WEB_CRON=$(crontab -u $WEB_USER -l 2>/dev/null | grep -E "(php|think)" | wc -l)

if [ "$ROOT_CRON" -gt 0 ]; then
    log_warning "发现root用户的PHP定时任务: $ROOT_CRON 个"
    crontab -l 2>/dev/null | grep -E "(php|think)"
    ISSUES_FOUND=$((ISSUES_FOUND + 1))
fi

if [ "$WEB_CRON" -gt 0 ]; then
    log_success "$WEB_USER 用户的PHP定时任务: $WEB_CRON 个"
else
    log_warning "未发现$WEB_USER 用户的PHP定时任务"
fi

echo ""
if [ "$ISSUES_FOUND" -eq 0 ]; then
    log_success "未发现权限问题！"
    exit 0
else
    log_warning "发现 $ISSUES_FOUND 个权限问题"
fi

# 步骤2: 询问是否修复
echo ""
log_step "2. 权限问题修复"
echo "----------------------------------------"

read -p "是否要修复发现的权限问题？(y/n): " -n 1 -r
echo ""

if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    log_info "修复已取消"
    exit 0
fi

# 步骤3: 执行修复
log_step "3. 执行权限修复"
echo "----------------------------------------"

# 修复runtime目录
if [ -d "$PROJECT_DIR/runtime" ]; then
    log_info "修复runtime目录权限..."
    chown -R $WEB_USER:$WEB_USER "$PROJECT_DIR/runtime"
    find "$PROJECT_DIR/runtime" -type d -exec chmod 755 {} \;
    find "$PROJECT_DIR/runtime" -type f -exec chmod 644 {} \;
    log_success "runtime目录权限修复完成"
fi

# 修复uploads目录
if [ -d "$PROJECT_DIR/public/uploads" ]; then
    log_info "修复uploads目录权限..."
    chown -R $WEB_USER:$WEB_USER "$PROJECT_DIR/public/uploads"
    chmod -R 755 "$PROJECT_DIR/public/uploads"
    log_success "uploads目录权限修复完成"
fi

# 步骤4: 预防措施
log_step "4. 设置预防措施"
echo "----------------------------------------"

# 创建权限监控定时任务
MONITOR_CRON="*/10 * * * * $PROJECT_DIR/scripts/monitor_permissions.sh"
EXISTING_MONITOR=$(crontab -u $WEB_USER -l 2>/dev/null | grep "monitor_permissions" || true)

if [ -z "$EXISTING_MONITOR" ]; then
    log_info "添加权限监控定时任务..."
    (crontab -u $WEB_USER -l 2>/dev/null; echo "$MONITOR_CRON") | crontab -u $WEB_USER -
    log_success "权限监控定时任务已添加"
else
    log_info "权限监控定时任务已存在"
fi

# 步骤5: 验证修复结果
log_step "5. 验证修复结果"
echo "----------------------------------------"

# 重新检查权限
REMAINING_ISSUES=0

if [ -d "$PROJECT_DIR/runtime" ]; then
    WRONG_FILES_AFTER=$(find "$PROJECT_DIR/runtime" ! -user $WEB_USER -type f 2>/dev/null | wc -l)
    if [ "$WRONG_FILES_AFTER" -eq 0 ]; then
        log_success "runtime目录权限修复成功"
    else
        log_warning "runtime目录仍有 $WRONG_FILES_AFTER 个权限问题文件"
        REMAINING_ISSUES=$((REMAINING_ISSUES + 1))
    fi
fi

if [ -d "$PROJECT_DIR/public/uploads" ]; then
    WRONG_UPLOADS_AFTER=$(find "$PROJECT_DIR/public/uploads" ! -user $WEB_USER -type f 2>/dev/null | wc -l)
    if [ "$WRONG_UPLOADS_AFTER" -eq 0 ]; then
        log_success "uploads目录权限修复成功"
    else
        log_warning "uploads目录仍有 $WRONG_UPLOADS_AFTER 个权限问题文件"
        REMAINING_ISSUES=$((REMAINING_ISSUES + 1))
    fi
fi

echo ""
echo "========================================"
if [ "$REMAINING_ISSUES" -eq 0 ]; then
    log_success "所有权限问题已修复完成！"
else
    log_warning "仍有 $REMAINING_ISSUES 个问题需要手动处理"
fi

echo ""
log_info "后续建议:"
echo "1. 确保所有定时任务都以 $WEB_USER 用户运行"
echo "2. 避免使用root用户执行 'php think' 命令"
echo "3. 定期运行: ./scripts/check_permissions.sh"
echo "4. 查看权限监控日志: tail -f /var/log/permission_monitor.log"
echo "========================================"
