<?php
/**
 * API 调用测试脚本
 * 用于测试 /api/order/editAddress 接口的运费限制功能
 */

echo "=== API 调用测试脚本 ===\n\n";

// 测试数据
$testCases = [
    [
        'name' => '测试场景1：原来包邮，修改后需要运费',
        'order_id' => 1,
        'original_address' => ['province' => 110000, 'city' => 110100, 'district' => 110101], // 北京
        'new_address' => ['province' => 650000, 'city' => 650100, 'district' => 650102], // 新疆
        'expected' => 'fail',
        'description' => '应该返回错误，不允许修改（运费从包邮变为收费）'
    ],
    [
        'name' => '测试场景2：原来需要运费，修改后包邮',
        'order_id' => 2,
        'original_address' => ['province' => 650000, 'city' => 650100, 'district' => 650102], // 新疆
        'new_address' => ['province' => 110000, 'city' => 110100, 'district' => 110101], // 北京
        'expected' => 'fail',
        'description' => '应该返回错误，不允许修改（运费从收费变为包邮）'
    ],
    [
        'name' => '测试场景3：原来需要运费，修改后运费不同',
        'order_id' => 3,
        'original_address' => ['province' => 650000, 'city' => 650100, 'district' => 650102], // 新疆
        'new_address' => ['province' => 540000, 'city' => 540100, 'district' => 540102], // 西藏
        'expected' => 'fail',
        'description' => '应该返回错误，不允许修改（运费金额发生变化）'
    ],
    [
        'name' => '测试场景4：原来包邮，修改后仍然包邮',
        'order_id' => 4,
        'original_address' => ['province' => 110000, 'city' => 110100, 'district' => 110101], // 北京
        'new_address' => ['province' => 310000, 'city' => 310100, 'district' => 310101], // 上海
        'expected' => 'success',
        'description' => '应该允许修改（运费都是包邮，无变化）'
    ],
    [
        'name' => '测试场景5：原来需要运费，修改后运费相同',
        'order_id' => 5,
        'original_address' => ['province' => 650000, 'city' => 650100, 'district' => 650102], // 新疆某地
        'new_address' => ['province' => 650000, 'city' => 650200, 'district' => 650202], // 新疆其他地区
        'expected' => 'success',
        'description' => '应该允许修改（运费金额相同）'
    ]
];

// 模拟 API 请求
function simulateApiRequest($testCase) {
    echo "测试：{$testCase['name']}\n";
    echo "描述：{$testCase['description']}\n";
    
    // 构建请求数据
    $postData = [
        'id' => $testCase['order_id'],
        'consignee' => '测试收件人',
        'province' => $testCase['new_address']['province'],
        'city' => $testCase['new_address']['city'],
        'district' => $testCase['new_address']['district'],
        'address' => '测试详细地址',
        'mobile' => '13800138000'
    ];
    
    echo "请求数据：" . json_encode($postData, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 模拟响应
    if ($testCase['expected'] === 'fail') {
        $response = [
            'code' => 0,
            'msg' => '当前订单为包邮，修改后的地址需要运费，不允许修改地址',
            'data' => []
        ];
    } else {
        $response = [
            'code' => 1,
            'msg' => '操作成功',
            'data' => []
        ];
    }
    
    echo "响应结果：" . json_encode($response, JSON_UNESCAPED_UNICODE) . "\n";
    echo "状态：" . ($response['code'] == 1 ? "✅ 成功" : "❌ 失败") . "\n";
    echo "---\n\n";
}

// 执行测试
foreach ($testCases as $testCase) {
    simulateApiRequest($testCase);
}

echo "=== 测试完成 ===\n\n";

// 实际 API 调用示例（需要在实际环境中测试）
echo "实际 API 调用示例：\n";
echo "POST /api/order/editAddress\n";
echo "Content-Type: application/json\n\n";
echo "请求体：\n";
echo json_encode([
    'id' => 1,
    'consignee' => '张三',
    'province' => 650000,
    'city' => 650100,
    'district' => 650102,
    'address' => '天山区某某街道123号',
    'mobile' => '13800138000'
], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

echo "预期响应（失败情况）：\n";
echo json_encode([
    'code' => 0,
    'msg' => '当前订单为包邮，修改后的地址需要运费，不允许修改地址',
    'data' => []
], JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n";
?>
