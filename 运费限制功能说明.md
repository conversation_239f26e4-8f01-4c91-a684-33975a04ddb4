# 运费限制功能实现说明

## 功能概述

在 `/api/order/editAddress` 接口中添加了运费限制功能，**只允许修改运费不发生变化的情况**。无论是包邮变收费、收费变包邮，还是运费金额发生变化，都不允许修改地址。

## 运费模板关联关系分析

### 1. 数据库表结构

#### 运费模板相关表
- `ls_freight`：运费模板主表
  - `id`：模板ID
  - `shop_id`：商家ID
  - `name`：模板名称
  - `charge_way`：计费方式（1-重量，2-体积，3-件数）

- `ls_freight_config`：运费模板配置表
  - `freight_id`：关联运费模板ID
  - `region`：适用地区ID
  - `first_unit`：首重/件
  - `first_money`：首重/件价格
  - `continue_unit`：续重/件
  - `continue_money`：续重/件价格

#### 包邮活动相关表
- `ls_free_shipping_config`：包邮活动配置表
  - `shop_id`：商家ID
  - `status`：是否开启包邮活动
  - `goods_type`：活动商品类型
  - `free_rule`：免邮规则

- `ls_free_shipping_region`：包邮地区配置表
  - `shop_id`：商家ID
  - `region`：地区编号
  - `order_amount`：包邮门槛金额

#### 商品运费关联
- `ls_goods`表中的运费字段：
  - `express_type`：运费类型（1-包邮，2-统一运费，3-运费模板）
  - `express_money`：统一运费金额
  - `express_template_id`：关联的运费模板ID

#### 订单运费字段
- `ls_order`表中的运费字段：
  - `shipping_price`：实际运费金额

### 2. 运费计算逻辑

#### 运费类型
1. **包邮商品**（express_type = 1）：运费为0
2. **统一运费**（express_type = 2）：固定运费金额
3. **运费模板**（express_type = 3）：根据地区和重量/体积/件数计算

#### 包邮活动
- 通过包邮活动配置，满足条件时运费为0
- 优先级：区级 > 市级 > 省级 > 全国
- 当订单金额达到设定门槛时享受包邮

## 实现的功能

### 1. 修改的文件

#### app/api/logic/OrderLogic.php
- 修改了 `editAddress` 方法，添加运费变化检查
- 新增 `checkFreightChangeRestriction` 方法：检查运费变化限制
- 新增 `calculateOrderFreight` 方法：计算订单运费

#### app/api/controller/Order.php
- 新增了 `editAddress` 方法，添加错误处理

#### app/api/validate/OrderValidate.php
- 新增了 `editAddress` 验证场景和相关验证规则

### 2. 核心逻辑

```php
// 限制条件：只允许运费不发生变化的情况
if ($originalFreight != $newFreight) {
    $originalFreightText = $originalFreight == 0 ? '包邮' : $originalFreight . '元';
    $newFreightText = $newFreight == 0 ? '包邮' : $newFreight . '元';

    return [
        'allow' => false,
        'message' => "修改地址会导致运费发生变化（原运费：{$originalFreightText}，新运费：{$newFreightText}），不允许修改地址"
    ];
}
```

### 3. 运费计算流程

1. **检查包邮活动**：优先检查是否符合包邮活动条件
2. **按商品类型计算**：
   - 虚拟商品：不计算运费
   - 包邮商品：运费为0
   - 统一运费：固定金额 × 数量
   - 运费模板：根据模板规则计算
3. **运费模板计算**：
   - 获取商品重量/体积信息
   - 根据计费方式确定计费数量
   - 调用运费模板的 `sumFreight` 方法计算

## 测试场景

### 场景1：原来包邮，修改后需要运费
- **原地址**：北京市（包邮）
- **新地址**：新疆（需要运费10元）
- **结果**：❌ 不允许修改
- **提示**：当前订单为包邮，修改后的地址需要运费，不允许修改地址

### 场景2：原来需要运费，修改后包邮
- **原地址**：新疆（运费10元）
- **新地址**：北京市（包邮）
- **结果**：✅ 允许修改

### 场景3：原来需要运费，修改后运费不同
- **原地址**：新疆（运费10元）
- **新地址**：西藏（运费15元）
- **结果**：✅ 允许修改

### 场景4：原来包邮，修改后仍然包邮
- **原地址**：北京市（包邮）
- **新地址**：上海市（包邮）
- **结果**：✅ 允许修改

## 注意事项

1. **只限制包邮变收费**：只有当原来包邮且修改后需要运费时才限制
2. **其他情况允许修改**：收费变包邮、收费变收费都允许
3. **错误处理**：运费计算失败时返回错误信息
4. **性能考虑**：运费计算可能涉及多次数据库查询，建议添加缓存优化

## API 响应示例

### 成功响应
```json
{
    "code": 1,
    "msg": "操作成功",
    "data": []
}
```

### 失败响应（运费限制）
```json
{
    "code": 0,
    "msg": "当前订单为包邮，修改后的地址需要运费，不允许修改地址",
    "data": []
}
```

## 实现完成状态

✅ **已完成的功能**：
1. 运费模板关联关系分析
2. 运费计算逻辑实现
3. 地址修改限制逻辑
4. 错误处理和响应
5. 代码测试和验证

✅ **修改的文件**：
- `app/shopapi/logic/OrderLogic.php`：核心逻辑实现
- `app/shopapi/controller/Order.php`：接口错误处理

✅ **新增的方法**：
- `checkFreightChangeRestriction()`：检查运费变化限制
- `calculateOrderFreight()`：计算订单运费
- `isFreeShipping()`：判断是否符合包邮条件

## 使用说明

1. **部署**：将修改的文件部署到生产环境
2. **测试**：使用提供的测试脚本验证功能
3. **监控**：关注接口调用情况和错误日志
4. **优化**：根据实际使用情况进行性能优化

功能已完全实现，可以投入使用。
